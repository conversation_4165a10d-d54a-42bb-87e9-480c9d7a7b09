<html>
<header>
<title>
  FreeType&nbsp;2 Project Files for Visual&nbsp;C++ 6.0 and 2002-2008
</title>

<body>
<h1>
  FreeType&nbsp;2 Project Files for Visual&nbsp;C++ 6.0 and 2002-2008
</h1>

<p>This directory contains project files <tt>freetype.dsp</tt> for
Visual C++ 6.0, and <tt>freetype.vcproj</tt> for Visual C++ 2002
through 2008, which you might need to upgrade automatically.
It compiles the following libraries from the FreeType 2.13.3 sources:</p>

<ul>
  <li>freetype.dll using 'Release' or 'Debug' configurations</li>
  <li>freetype.lib using 'Release Static' or 'Debug Static' configurations</li>
</ul>

<p>Build directories and target files are placed in the top-level
<tt>objs</tt> directory.</p>

<p>Be sure to extract the files with the Windows (CR+LF) line endings.  ZIP
archives are already stored this way, so no further action is required.  If
you use some <tt>.tar.*z</tt> archives, be sure to configure your extracting
tool to convert the line endings.  For example, with <a
href="https://www.winzip.com">WinZip</a>, you should activate the <em>TAR
file smart CR/LF Conversion</em> option.  Alternatively, you may consider
using the <tt>unix2dos</tt> or <tt>u2d</tt> utilities that are floating
around, which specifically deal with this particular problem.

<p>Build directories are placed in the top-level <tt>objs</tt>
directory.</p>

</body>
</html>
