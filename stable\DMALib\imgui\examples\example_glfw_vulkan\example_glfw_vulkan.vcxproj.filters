﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="imgui">
      <UniqueIdentifier>{20b90ce4-7fcb-4731-b9a0-075f875de82d}</UniqueIdentifier>
    </Filter>
    <Filter Include="sources">
      <UniqueIdentifier>{f18ab499-84e1-499f-8eff-9754361e0e52}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="main.cpp">
      <Filter>sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\imgui.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\imgui_demo.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\imgui_draw.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\imgui_tables.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\imgui_widgets.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\backends\imgui_impl_glfw.cpp">
      <Filter>sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\backends\imgui_impl_vulkan.cpp">
      <Filter>sources</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\imconfig.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\imgui.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\imgui_internal.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\backends\imgui_impl_glfw.h">
      <Filter>sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\backends\imgui_impl_vulkan.h">
      <Filter>sources</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\README.txt" />
    <None Include="..\..\misc\debuggers\imgui.natvis">
      <Filter>imgui</Filter>
    </None>
    <None Include="..\..\misc\debuggers\imgui.natstepfilter">
      <Filter>imgui</Filter>
    </None>
  </ItemGroup>
</Project>