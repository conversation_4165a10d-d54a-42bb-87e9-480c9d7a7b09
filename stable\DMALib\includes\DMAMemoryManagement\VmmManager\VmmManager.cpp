#include "./VmmManager.h"

VMM_HANDLE DMAMem::VmmManager::getVmm() {
	if (!hVMM) {
		hVMM = initialize();
	}
	return hVMM;
}


VMM_HANDLE DMAMem::VmmManager::initialize()
{
	VMMDLL_CloseAll();
	std::cout << " [ + ] Connecting to DMA Card..." << std::endl;

	// Add -norefresh parameter for manual refresh control
	LPSTR args[] = { LPSTR(""), LPSTR("-device"), LPSTR("fpga://algo=0"), LPSTR("-norefresh") };

	VMM_HANDLE handle = VMMDLL_Initialize(4, args);
	if (!handle)
		std::cout << "[ ! ] Vmm Initialization Failed..." << std::endl;
	else {
		std::cout << " [ + ] Connected to DMA Card..." << std::endl;

		// Start manual refresh loop to prevent 5-second freezes
		if (manual_refresh) {
			std::cout << " [ + ] Starting manual refresh loop to prevent 5-second freezes..." << std::endl;
			std::thread(&VmmManager::RefreshLoop, this).detach();
			std::cout << " [ + ] Refresh loop started successfully." << std::endl;
		}
	}

	return handle;
}

BOOL DMAMem::VmmManager::readMemory(DWORD pid, QWORD remoteAddress, void* destination, int size, ULONG64 flags)
{
	return VMMDLL_MemReadEx(this->getVmm(), pid, remoteAddress, (PBYTE)destination, size, NULL, flags);
}

VMMDLL_SCATTER_HANDLE DMAMem::VmmManager::initializeScatter(DWORD pid)
{
	return initializeScatter(pid, VMMDLL_FLAG_NOCACHE);
}

VMMDLL_SCATTER_HANDLE DMAMem::VmmManager::initializeScatter(DWORD pid, ULONG64 flags)
{
	return VMMDLL_Scatter_Initialize(this->getVmm(), pid, flags);
}

void DMAMem::VmmManager::addScatterRead(VMMDLL_SCATTER_HANDLE handle, QWORD remoteAddress, int size, void* destination)
{
	VMMDLL_Scatter_PrepareEx(handle, remoteAddress, size, (PBYTE)destination, nullptr);
}

void DMAMem::VmmManager::executeScatter(VMMDLL_SCATTER_HANDLE handle)
{
	VMMDLL_Scatter_ExecuteRead(handle);
	VMMDLL_Scatter_CloseHandle(handle);
}

uint64_t DMAMem::VmmManager::GetCurrentTime()
{
	return std::chrono::duration_cast<std::chrono::microseconds>(
		std::chrono::steady_clock::now().time_since_epoch()
	).count();
}

void DMAMem::VmmManager::RefreshLoop()
{
	uint64_t last_slow = 0;
	uint64_t last_medium = 0;
	uint64_t last_fast = 0;

	while (this->hVMM != NULL) {
		if (wants_no_refresh) {
			break;
		}

		auto now = GetCurrentTime();

		if (!last_fast) {
			last_slow = now;
			last_fast = now;
			continue;
		}

		if (wants_full_refresh) {
			if (now - last_slow > 300000) { // 300ms for active process
				VMMDLL_ConfigSet(this->hVMM, VMMDLL_OPT_REFRESH_FREQ_SLOW, 1);
				last_slow = GetCurrentTime();
			}
		}
		else {
			last_slow = 0;
		}

		if (wants_refresh) {
			if (now - last_medium > 80000) { // 80ms
				VMMDLL_ConfigSet(this->hVMM, VMMDLL_OPT_REFRESH_FREQ_MEDIUM, 1);
				last_medium = GetCurrentTime();
			}
		}
		else {
			last_medium = 0;
		}

		if (now - last_fast > 5000) { // 5ms
			VMMDLL_ConfigSet(this->hVMM, VMMDLL_OPT_REFRESH_FREQ_TLB_PARTIAL, 1);

			if (wants_full_refresh) {
				VMMDLL_ConfigSet(this->hVMM, VMMDLL_OPT_REFRESH_FREQ_MEM, 1);
			}

			last_fast = GetCurrentTime();
		}

		Sleep(500); // Sleep 500ms between refresh cycles
	}
}
