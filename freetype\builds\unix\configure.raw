# This file is part of the FreeType project.
#
# Process this file with autoconf to produce a configure script.
#
# Copyright (C) 2001-2024 by
# <PERSON>, <PERSON>, and <PERSON>.
#
# This file is part of the FreeType project, and may only be used, modified,
# and distributed under the terms of the FreeType project license,
# LICENSE.TXT.  By continuing to use, modify, or distribute this file you
# indicate that you have read the license and understand and accept it
# fully.

AC_INIT([FreeType], [@VERSION@], [<EMAIL>], [freetype])
AC_CONFIG_SRCDIR([ftconfig.h.in])


# Don't forget to update `docs/VERSIONS.TXT'!

version_info='26:2:20'
AC_SUBST([version_info])
ft_version=`echo $version_info | tr : .`
AC_SUBST([ft_version])


# checks for system type

AC_CANONICAL_HOST


# checks for programs

AC_PROG_CC
AC_PROG_CPP
AC_SUBST(EXEEXT)

PKG_PROG_PKG_CONFIG([0.24])

LT_INIT(win32-dll)
AC_CHECK_HEADER([windows.h], [LT_PROG_RC])


# checks for native programs to generate building tool

if test ${cross_compiling} = yes; then
  AC_CHECK_PROG(CC_BUILD, ${build}-gcc, ${build}-gcc)
  test -z "${CC_BUILD}" && AC_CHECK_PROG(CC_BUILD, gcc, gcc)
  test -z "${CC_BUILD}" && AC_CHECK_PROG(CC_BUILD, cc, cc, , , /usr/ucb/cc)
  test -z "${CC_BUILD}" && AC_CHECK_PROG(CC_BUILD, clang, clang)
  test -z "${CC_BUILD}" && AC_MSG_ERROR([cannot find native C compiler])

  AC_MSG_CHECKING([for suffix of native executables])
  rm -f a.* b.* a_out.exe conftest.*
  echo > conftest.c "int main(void) { return 0;}"
  ${CC_BUILD} conftest.c || AC_MSG_ERROR([native C compiler is not working])
  rm -f conftest.c
  if test -x a.out -o -x b.out -o -x conftest; then
    EXEEXT_BUILD=""
  elif test -x a_out.exe -o -x conftest.exe; then
    EXEEXT_BUILD=".exe"
  elif test -x conftest.*; then
    EXEEXT_BUILD=`echo conftest.* | sed -n '1s/^.*\././'`
  fi
  rm -f a.* b.* a_out.exe conftest.*
  AC_MSG_RESULT($EXEEXT_BUILD)
  CFLAGS_BUILD=
  LDFLAGS_BUILD=
else
  CC_BUILD=${CC}
  CFLAGS_BUILD=${CFLAGS}
  LDFLAGS_BUILD=${LDFLAGS}
  EXEEXT_BUILD=${EXEEXT}
fi

AC_SUBST(CC_BUILD)
AC_SUBST(CFLAGS_BUILD)
AC_SUBST(LDFLAGS_BUILD)
AC_SUBST(EXEEXT_BUILD)


# Since these files will be eventually called from another directory (namely
# from the top level) we make the path of the scripts absolute.
#
# This small code snippet has been taken from automake's `ylwrap' script.

AC_PROG_INSTALL
case "$INSTALL" in
[[\\/]]* | ?:[[\\/]]*)
  ;;
*[[\\/]]*)
  INSTALL="`pwd`/$INSTALL"
  ;;
esac

AC_PROG_MKDIR_P
case "$MKDIR_P" in
[[\\/]]* | ?:[[\\/]]*)
  ;;
*[[\\/]]*)
  MKDIR_P="`pwd`/$MKDIR_P"
  ;;
esac


# checks for header files

AC_CHECK_HEADERS([fcntl.h unistd.h])


# checks for typedefs, structures, and compiler characteristics

AC_C_CONST

AC_ARG_ENABLE([freetype-config],
  AS_HELP_STRING([--enable-freetype-config], [install freetype-config]),
  [case "${enableval}" in
    yes) enable_freetype_config="TRUE" ;;
    no)  enable_freetype_config="FALSE" ;;
    *)   AC_MSG_ERROR([unknown value '${enableval}' passed with --enable-freetype-config]) ;;
   esac], [enable_freetype_config="FALSE"])

AC_SUBST(INSTALL_FT2_CONFIG, [$enable_freetype_config])

# checks for library functions

AC_SYS_LARGEFILE

# Here we check whether we can use our mmap file component.
#
# Note that `ftsystem.c` for Windows has its own mmap-like implementation
# not covered by `AC_FUNC_MMAP` and/or `FT_UNMAP_PARAM`.

AC_ARG_ENABLE([mmap],
  AS_HELP_STRING([--disable-mmap],
                 [do not check mmap() and do not use]),
  [enable_mmap="no"], [enable_mmap="yes"])
if test "x${enable_mmap}" != "xno"; then
  case "$host" in
  *-*-mingw*)
    AC_MSG_CHECKING([for working mmap])
    AC_MSG_RESULT([using MapViewOfFile in Windows])
    FTSYS_SRC='$(TOP_DIR)/builds/windows/ftsystem.c'
    ;;
  *)
    AC_FUNC_MMAP
    if test "$ac_cv_func_mmap_fixed_mapped" = "yes"; then
      FTSYS_SRC='$(PLATFORM_DIR)/ftsystem.c'

      AC_CHECK_DECLS([munmap],
        [],
        [],
        [

#ifdef HAVE_UNISTD_H
#include <unistd.h>
#endif
#include <sys/mman.h>

        ])

      FT_MUNMAP_PARAM
    fi
    ;;
  esac
fi

if test -z "$FTSYS_SRC"; then
  FTSYS_SRC='$(BASE_DIR)/ftsystem.c'
fi
AC_SUBST([FTSYS_SRC])


# get compiler flags right
#
#   We try to make the compiler work for C99-strict source.  Even if the
#   C compiler is gcc and C99 flags are available, some system headers
#   might be broken in C99 mode.  We have to check whether compilation
#   finishes successfully.
#
if test "x$GCC" = xyes; then
  XX_CFLAGS="-Wall"
  case "$host" in
  *-*-mingw*)
    XX_ANSIFLAGS="-pedantic"
    ;;
  *-*-aix*)
    XX_ANSIFLAGS="-pedantic"
    ;;
  *)
    XX_ANSIFLAGS=""

    for a in "-pedantic" "-std=c99"
    do
      AC_MSG_CHECKING([$CC compiler flag ${a} to assure ANSI C99 works correctly])
      orig_CFLAGS="${CFLAGS}"
      CFLAGS="${CFLAGS} ${XX_ANSIFLAGS} ${a}"
      AC_COMPILE_IFELSE([
        AC_LANG_PROGRAM([

#include <stdio.h>

          ],
          [

            {
              puts( "" );
              return 0;
            }

          ])],
        [AC_MSG_RESULT([ok, adding to XX_ANSIFLAGS])
         XX_ANSIFLAGS="${XX_ANSIFLAGS} ${a}"
        ],
        [AC_MSG_RESULT([no])])
      CFLAGS="${orig_CFLAGS}"
    done
    ;;
  esac
else
  case "$host" in
  *-dec-osf*)
    CFLAGS=
    XX_CFLAGS="-std1 -g3"
    XX_ANSIFLAGS=
    ;;
  *)
    XX_CFLAGS=
    XX_ANSIFLAGS=
    ;;
  esac
fi
AC_SUBST([XX_CFLAGS])
AC_SUBST([XX_ANSIFLAGS])


# It is recommended that shared libraries hide symbols except those with
# explicit __attribute__((visibility("default"))).
#
found_visibility_flag=no
AC_MSG_CHECKING([for -fvisibility=hidden compiler flag])
orig_CFLAGS="${CFLAGS}"
CFLAGS="${CFLAGS} -fvisibility=hidden -Werror"
AC_COMPILE_IFELSE([
  AC_LANG_PROGRAM([

__attribute__(( visibility( "default" ) )) int foo( void );
__attribute__(( visibility( "hidden" ) )) int bar( void );

int foo( void ) { return 0; }
int bar( void ) { return 1; }

    ],
    [])],
  [found_visibility_flag=yes
   CFLAGS="${orig_CFLAGS} -fvisibility=hidden"
   AC_MSG_RESULT(yes)
  ],
  [CFLAGS="${orig_CFLAGS}"
   AC_MSG_RESULT(no)])

if test "${found_visibility_flag}" = "no"; then
  AC_MSG_CHECKING([for -xldscope=hidden compiler flag])
  orig_CFLAGS="${CFLAGS}"
  CFLAGS="${CFLAGS} -xldscope=hidden"
  AC_LINK_IFELSE([AC_LANG_PROGRAM([], [])],
                 [found_visibility_flag=yes
                  AC_MSG_RESULT(yes)],
                 [CFLAGS="${orig_CFLAGS}"
                  AC_MSG_RESULT(no)])
fi

# All library tests below try `pkg-config' first.  If that fails, a function
# from the library is tested in the traditional autoconf way (zlib, bzip2),
# or a config script is called (libpng).
#
# The `xxx_reqpriv' variables are for the `Requires.private' field in
# `freetype2.pc'.  The `xxx_libspriv' variables are for the `Libs.private'
# field in `freetype2.pc' if pkg-config doesn't find a proper .pc file.
#
# The `xxx_libsstaticconf' variables are for the `freetype-config' script.
#
# Note that a call to PKG_CHECK_MODULES(XXX, ...) sets and creates the
# output variables `XXX_CFLAGS' and `XXX_LIBS'.  In case one or both are set
# for a library by the user, no entry for this library is added to
# `Requires.private'.  Instead, it gets added to `Libs.private'


# check for system zlib

AC_ARG_WITH([zlib],
  [AS_HELP_STRING([--with-zlib=@<:@yes|no|auto@:>@],
                  [use system zlib instead of internal library @<:@default=auto@:>@])],
  [], [with_zlib=auto])

have_zlib=no
if test x"$with_zlib" = xyes -o x"$with_zlib" = xauto; then
  zlib_pkg="zlib"
  have_zlib_pkg=no

  if test x"$ZLIB_CFLAGS" = x -a x"$ZLIB_LIBS" = x; then
    PKG_CHECK_EXISTS([$zlib_pkg], [have_zlib_pkg=yes])
  fi
  PKG_CHECK_MODULES([ZLIB], [$zlib_pkg],
                    [have_zlib="yes (pkg-config)"], [:])

  if test $have_zlib_pkg = yes; then
    # we have zlib.pc
    zlib_reqpriv="$zlib_pkg"
    zlib_libspriv=
    zlib_libsstaticconf=`$PKG_CONFIG --static --libs "$zlib_pkg"`
  else
    zlib_reqpriv=

    if test "$have_zlib" != no; then
      # ZLIB_CFLAGS and ZLIB_LIBS are set by the user
      zlib_libspriv="$ZLIB_LIBS"
      zlib_libsstaticconf="$ZLIB_LIBS"
      have_zlib="yes (ZLIB_CFLAGS and ZLIB_LIBS)"
    else
      # fall back to standard autoconf test
      AC_CHECK_LIB([z],
                   [gzsetparams],
                   [AC_CHECK_HEADER([zlib.h],
                                    [have_zlib="yes (autoconf test)"
                                     zlib_libspriv="-lz"
                                     zlib_libsstaticconf="$zlib_libspriv"
                                     ZLIB_LIBS="$zlib_libspriv"])])
    fi
  fi
fi

if test x"$with_zlib" = xyes -a "$have_zlib" = no; then
  AC_MSG_ERROR([external zlib support requested but library not found])
fi

SYSTEM_ZLIB=
if test "$have_zlib" != no; then
  SYSTEM_ZLIB=yes
fi
AC_SUBST([SYSTEM_ZLIB])


# check for system libbz2

AC_ARG_WITH([bzip2],
  [AS_HELP_STRING([--with-bzip2=@<:@yes|no|auto@:>@],
                  [support bzip2 compressed fonts @<:@default=auto@:>@])],
  [], [with_bzip2=auto])

have_bzip2=no
if test x"$with_bzip2" = xyes -o x"$with_bzip2" = xauto; then
  bzip2_pkg="bzip2"
  have_bzip2_pkg=no

  if test x"$BZIP2_CFLAGS" = x -a x"$BZIP2_LIBS" = x; then
    PKG_CHECK_EXISTS([$bzip2_pkg], [have_bzip2_pkg=yes])
  fi
  PKG_CHECK_MODULES([BZIP2], [$bzip2_pkg],
                    [have_bzip2="yes (pkg-config)"], [:])

  if test $have_bzip2_pkg = yes; then
    # we have bzip2.pc
    bzip2_reqpriv="$bzip2_pkg"
    bzip2_libspriv=
    bzip2_libsstaticconf=`$PKG_CONFIG --static --libs "$bzip2_pkg"`
  else
    bzip2_reqpriv=

    if test "$have_bzip2" != no; then
      # BZIP2_CFLAGS and BZIP2_LIBS are set by the user
      bzip2_libspriv="$BZIP2_LIBS"
      bzip2_libsstaticconf="$BZIP2_LIBS"
      have_bzip2="yes (BZIP2_CFLAGS and BZIP2_LIBS)"
    else
      # fall back to standard autoconf test
      AC_CHECK_LIB([bz2],
                   [BZ2_bzDecompress],
                   [AC_CHECK_HEADER([bzlib.h],
                                    [have_bzip2="yes (autoconf test)"
                                     bzip2_libspriv="-lbz2"
                                     bzip2_libsstaticconf="$bzip2_libspriv"
                                     BZIP2_LIBS="$bzip2_libspriv"])])
    fi
  fi
fi

if test x"$with_bzip2" = xyes -a "$have_bzip2" = no; then
  AC_MSG_ERROR([bzip2 support requested but library not found])
fi


# check for system libpng

AC_ARG_WITH([png],
  [AS_HELP_STRING([--with-png=@<:@yes|no|auto@:>@],
                  [support png compressed OpenType embedded bitmaps @<:@default=auto@:>@])],
  [], [with_png=auto])

have_libpng=no
if test x"$with_png" = xyes -o x"$with_png" = xauto; then
  libpng_pkg="libpng"
  have_libpng_pkg=no

  if test x"$LIBPNG_CFLAGS" = x -a x"$LIBPNG_LIBS" = x; then
    PKG_CHECK_EXISTS([$libpng_pkg], [have_libpng_pkg=yes])
  fi
  PKG_CHECK_MODULES([LIBPNG], [$libpng_pkg],
                    [have_libpng="yes (pkg-config)"], [:])

  if test $have_libpng_pkg = yes; then
    # we have libpng.pc
    libpng_reqpriv="$libpng_pkg"
    libpng_libspriv=
    libpng_libsstaticconf=`$PKG_CONFIG --static --libs "$libpng_pkg"`
  else
    libpng_reqpriv=

    if test "$have_libpng" != no; then
      # LIBPNG_CFLAGS and LIBPNG_LIBS are set by the user
      libpng_libspriv="$LIBPNG_LIBS"
      libpng_libsstaticconf="$LIBPNG_LIBS"
      have_libpng="yes (LIBPNG_CFLAGS and LIBPNG_LIBS)"
    else
      # fall back to config script
      AC_CHECK_PROG(have_libpng, [libpng-config], [yes (libpng-config)], [no])
      if test "$have_libpng" != no; then
        LIBPNG_CFLAGS=`libpng-config --cflags`
        LIBPNG_LIBS=`libpng-config --ldflags`
        libpng_libspriv=`libpng-config --static --ldflags`
        libpng_libsstaticconf="$libpng_libspriv"
      fi
    fi
  fi
fi

if test x"$with_png" = xyes -a "$have_libpng" = no; then
  AC_MSG_ERROR([libpng support requested but library not found])
fi


# check for system libharfbuzz

AC_ARG_WITH([harfbuzz],
  [AS_HELP_STRING([--with-harfbuzz=@<:@yes|dynamic|no|auto@:>@],
                  [improve auto-hinting of OpenType fonts @<:@default=auto@:>@])],
  [], [with_harfbuzz=auto])

have_harfbuzz=no
if test x"$with_harfbuzz" = xyes -o x"$with_harfbuzz" = xauto; then
  harfbuzz_pkg="harfbuzz >= 2.0.0"
  have_harfbuzz_pkg=no

  if test x"$HARFBUZZ_CFLAGS" = x -a x"$HARFBUZZ_LIBS" = x; then
    PKG_CHECK_EXISTS([$harfbuzz_pkg], [have_harfbuzz_pkg=yes])
  fi
  PKG_CHECK_MODULES([HARFBUZZ], [$harfbuzz_pkg],
                    [have_harfbuzz="yes (pkg-config)"], [:])

  if test $have_harfbuzz_pkg = yes; then
    # we have harfbuzz.pc
    harfbuzz_reqpriv="$harfbuzz_pkg"
    harfbuzz_libspriv=
    harfbuzz_libsstaticconf=`$PKG_CONFIG --static --libs "$harfbuzz_pkg"`
  else
    harfbuzz_reqpriv=

    if test "$have_harfbuzz" != no; then
      # HARFBUZZ_CFLAGS and HARFBUZZ_LIBS are set by the user
      harfbuzz_libspriv="$HARFBUZZ_LIBS"
      harfbuzz_libsstaticconf="$HARFBUZZ_LIBS"
      have_harfbuzz="yes (HARFBUZZ_CFLAGS and HARFBUZZ_LIBS)"
    else
      # since HarfBuzz is quite a new library we don't fall back to a
      # different test; additionally, it has too many dependencies
      :
    fi
  fi
fi

have_harfbuzz_dynamic=no
if test x"$have_harfbuzz" = xno; then
  if test x"$with_harfbuzz" = xdynamic -o x"$with_harfbuzz" = xauto; then
    case "$host" in
    *-*-mingw*)
      # Windows uses its own LoadLibrary(), set variables to dynamic.
      have_harfbuzz_dynamic=yes
      have_harfbuzz="yes (dynamic)"
      ;;
    *)
      # Check for libdl
      AC_CHECK_LIB([c],
                   [dlopen],
                   [have_harfbuzz_dynamic=yes],
                   AC_CHECK_LIB([dl],
                                [dlopen],
                                [have_harfbuzz_dynamic=yes
                                 need_libdl=yes]))
      if test x"$have_harfbuzz_dynamic" = xyes; then
        have_harfbuzz="yes (dynamic)"
        HARFBUZZ_CFLAGS=
        if test x"$need_libdl" = xyes; then
          HARFBUZZ_LIBS="-ldl"
        fi
      fi
      ;;
    esac
  fi
fi

if test x"$have_harfbuzz" = xno; then
  if test x"$with_harfbuzz" != xno -a "$with_harfbuzz" != xauto; then
    AC_MSG_ERROR([harfbuzz support requested but library not found])
  fi
fi

# check for system libbrotlidec

AC_ARG_WITH([brotli],
  [AS_HELP_STRING([--with-brotli=@<:@yes|no|auto@:>@],
                  [support decompression of WOFF2 streams @<:@default=auto@:>@])],
  [], [with_brotli=auto])

have_brotli=no
if test x"$with_brotli" = xyes -o x"$with_brotli" = xauto; then
  brotli_pkg="libbrotlidec"
  have_brotli_pkg=no

  if test x"$BROTLI_CFLAGS" = x -a x"$BROTLI_LIBS" = x; then
    PKG_CHECK_EXISTS([$brotli_pkg], [have_brotli_pkg=yes])
  fi
  PKG_CHECK_MODULES([BROTLI], [$brotli_pkg],
                    [have_brotli="yes (pkg-config)"], [:])

  if test $have_brotli_pkg = yes; then
    # we have libbrotlidec.pc
    brotli_reqpriv="$brotli_pkg"
    brotli_libspriv=
    brotli_libsstaticconf=`$PKG_CONFIG --static --libs "$brotli_pkg"`
  else
    brotli_reqpriv=

    if test "$have_brotli" != no; then
      # BROTLI_CFLAGS and BROTLI_LIBS are set by the user
      brotli_libspriv="$BROTLI_LIBS"
      brotli_libsstaticconf="$BROTLI_LIBS"
      have_brotli="yes (BROTLI_CFLAGS and BROTLI_LIBS)"
    else
      # since Brotli is quite a new library we don't fall back to a
      # different test
      :
    fi
  fi
fi

if test x"$with_brotli" = xyes -a "$have_brotli" = no; then
  AC_MSG_ERROR([brotli support requested but library not found])
fi


# Checks for the demo programs.
#
# FreeType doesn't need this.  However, since the demo program repository
# doesn't come with a `configure` script of its own, we integrate the tests
# here for simplicity.

# We need `clock_gettime` from 'librt' for the `ftbench` demo program.
#
# The code is modeled after gnulib's file `clock_time.m4`, ignoring
# very old Solaris systems.
LIB_CLOCK_GETTIME=
AC_SEARCH_LIBS([clock_gettime],
               [rt],
               [test "$ac_cv_search_clock_gettime" = "none required" \
                || LIB_CLOCK_GETTIME=$ac_cv_search_clock_gettime])

FT_DEMO_CFLAGS=""
FT_DEMO_LDFLAGS="$LIB_CLOCK_GETTIME"

# 'librsvg' is needed to demonstrate SVG support.
AC_ARG_WITH([librsvg],
  [AS_HELP_STRING([--with-librsvg=@<:@yes|no|auto@:>@],
                  [support OpenType SVG fonts in FreeType demo programs @<:@default=auto@:>@])],
  [], [with_librsvg=auto])

have_librsvg=no
if test x"$with_librsvg" = xyes -o x"$with_librsvg" = xauto; then
  PKG_CHECK_MODULES([LIBRSVG], [librsvg-2.0 >= 2.46.0],
                    [have_librsvg="yes (pkg-config)"], [:])

  if test "$have_librsvg" != no; then
    FT_DEMO_CFLAGS="$FT_DEMO_CFLAGS $LIBRSVG_CFLAGS -DHAVE_LIBRSVG"
    FT_DEMO_LDFLAGS="$FT_DEMO_LDFLAGS $LIBRSVG_LIBS"
  fi
fi

if test x"$with_librsvg" = xyes -a "$have_librsvg" = no; then
  AC_MSG_ERROR([librsvg support requested but library not found])
fi

AC_SUBST([FT_DEMO_CFLAGS])
AC_SUBST([FT_DEMO_LDFLAGS])


# Some options handling SDKs/archs in CFLAGS should be copied
# to LDFLAGS. Apple TechNote 2137 recommends to include these
# options in CFLAGS but not in LDFLAGS.

save_config_args=$*
set dummy ${CFLAGS}
i=1
while test $i -le $#
do
  c=$1

  case "${c}" in
  -isysroot|-arch) # options taking 1 argument
    a=$2
    AC_MSG_CHECKING([whether CFLAGS and LDFLAGS share ${c} ${a}])
    if expr " ${LDFLAGS} " : ".* ${c} *${a}.*" > /dev/null
    then
      AC_MSG_RESULT([yes])
    else
      AC_MSG_RESULT([no, copy to LDFLAGS])
      LDFLAGS="${LDFLAGS} ${c} ${a}"
    fi
    shift 1
    ;;
  -m32|-m64|-march=*|-mcpu=*) # options taking no argument
    AC_MSG_CHECKING([whether CFLAGS and LDFLAGS share ${c}])
    if expr " ${LDFLAGS} " : ".* ${c} *${a}.*" > /dev/null
    then
      AC_MSG_RESULT([yes])
    else
      AC_MSG_RESULT([no, copy to LDFLAGS])
      LDFLAGS="${LDFLAGS} ${c}"
    fi
    ;;
  # *)
  #   AC_MSG_RESULT([${c} is not copied to LDFLAGS])
  #   ;;
  esac

  shift 1
done
set ${save_config_args}


# Whether to use Mac OS resource-based fonts.

ftmac_c="" # src/base/ftmac.c should not be included in makefiles by default

AC_ARG_WITH([old-mac-fonts],
  AS_HELP_STRING([--with-old-mac-fonts],
                 [allow Mac resource-based fonts to be used]))
if test x$with_old_mac_fonts = xyes; then
  orig_LDFLAGS="${LDFLAGS}"
  AC_MSG_CHECKING([CoreServices & ApplicationServices of Mac OS X])
  ft2_extra_libs="-Wl,-framework,CoreServices -Wl,-framework,ApplicationServices"
  LDFLAGS="$LDFLAGS $ft2_extra_libs"
  AC_LINK_IFELSE([
    AC_LANG_PROGRAM([

#if defined(__GNUC__) && defined(__APPLE_CC__)
# include <CoreServices/CoreServices.h>
# include <ApplicationServices/ApplicationServices.h>
#else
# include <ConditionalMacros.h>
# include <Files.h>
#endif

      ],
      [

        short res = 0;


        UseResFile( res );

      ])],
    [AC_MSG_RESULT([ok])
     ftmac_c='ftmac.c'
     AC_MSG_CHECKING([whether OS_INLINE macro is ANSI compatible])
     orig_CFLAGS="$CFLAGS -DFT_MACINTOSH"
     CFLAGS="$CFLAGS $XX_CFLAGS $XX_ANSIFLAGS"
     AC_COMPILE_IFELSE([
       AC_LANG_PROGRAM([

#if defined(__GNUC__) && defined(__APPLE_CC__)
# include <CoreServices/CoreServices.h>
# include <ApplicationServices/ApplicationServices.h>
#else
# include <ConditionalMacros.h>
# include <Files.h>
#endif

         ],
         [

           /* OSHostByteOrder() is typed as OS_INLINE */
           int32_t  os_byte_order = OSHostByteOrder();


           if ( OSBigEndian != os_byte_order )
             return 1;

         ])],
       [AC_MSG_RESULT([ok])
        CFLAGS="$orig_CFLAGS"
        CFLAGS="$CFLAGS -DHAVE_ANSI_OS_INLINE=1"
       ],
       [AC_MSG_RESULT([no, ANSI incompatible])
        CFLAGS="$orig_CFLAGS"
       ])
     AC_MSG_CHECKING([type ResourceIndex])
     orig_CFLAGS="$CFLAGS"
     CFLAGS="$CFLAGS $XX_CFLAGS $XX_ANSIFLAGS"
     AC_COMPILE_IFELSE([
       AC_LANG_PROGRAM([

#if defined(__GNUC__) && defined(__APPLE_CC__)
# include <CoreServices/CoreServices.h>
# include <ApplicationServices/ApplicationServices.h>
#else
# include <ConditionalMacros.h>
# include <Files.h>
# include <Resources.h>
#endif

         ],
         [

           ResourceIndex i = 0;
           return i;

         ])],
       [AC_MSG_RESULT([ok])
        CFLAGS="$orig_CFLAGS"
        CFLAGS="$CFLAGS -DHAVE_TYPE_RESOURCE_INDEX=1"
       ],
       [AC_MSG_RESULT([no])
        CFLAGS="$orig_CFLAGS"
        CFLAGS="$CFLAGS -DHAVE_TYPE_RESOURCE_INDEX=0"
       ])],
    [AC_MSG_RESULT([not found])
     ft2_extra_libs=""
     LDFLAGS="${orig_LDFLAGS}"
     CFLAGS="$CFLAGS -DDARWIN_NO_CARBON"])
else
  case x$host_os in
  xdarwin*)
    dnl AC_MSG_WARN([host system is MacOS but configured to build without Carbon])
    CFLAGS="$CFLAGS -DDARWIN_NO_CARBON"
    ;;
  *)
    ;;
  esac
fi


# Whether to use FileManager, which is deprecated since Mac OS X 10.4.

AC_ARG_WITH([fsspec],
  AS_HELP_STRING([--with-fsspec],
                 [use obsolete FSSpec API of MacOS, if available (default=yes)]))
if test x$with_fsspec = xno; then
  CFLAGS="$CFLAGS -DHAVE_FSSPEC=0"
elif test x$with_old_mac_fonts = xyes -a x$with_fsspec != x; then
  AC_MSG_CHECKING([FSSpec-based FileManager])
  AC_LINK_IFELSE([
    AC_LANG_PROGRAM([

#if defined(__GNUC__) && defined(__APPLE_CC__)
# include <CoreServices/CoreServices.h>
# include <ApplicationServices/ApplicationServices.h>
#else
# include <ConditionalMacros.h>
# include <Files.h>
#endif

      ],
      [

        FCBPBPtr          paramBlock;
        short             vRefNum;
        long              dirID;
        ConstStr255Param  fileName;
        FSSpec*           spec;


        /* FSSpec functions: deprecated since Mac OS X 10.4 */
        PBGetFCBInfoSync( paramBlock );
        FSMakeFSSpec( vRefNum, dirID, fileName, spec );

      ])],
    [AC_MSG_RESULT([ok])
     CFLAGS="$CFLAGS -DHAVE_FSSPEC=1"],
    [AC_MSG_RESULT([not found])
     CFLAGS="$CFLAGS -DHAVE_FSSPEC=0"])
fi


# Whether to use FileManager in Carbon since MacOS 9.x.

AC_ARG_WITH([fsref],
  AS_HELP_STRING([--with-fsref],
                 [use Carbon FSRef API of MacOS, if available (default=yes)]))
if test x$with_fsref = xno; then
  AC_MSG_WARN([
*** WARNING
    FreeType2 built without FSRef API cannot load
    data-fork fonts on MacOS, except of XXX.dfont.
    ])
  CFLAGS="$CFLAGS -DHAVE_FSREF=0"
elif test x$with_old_mac_fonts = xyes -a x$with_fsref != x; then
  AC_MSG_CHECKING([FSRef-based FileManager])
  AC_LINK_IFELSE([
    AC_LANG_PROGRAM([

#if defined(__GNUC__) && defined(__APPLE_CC__)
# include <CoreServices/CoreServices.h>
# include <ApplicationServices/ApplicationServices.h>
#else
# include <ConditionalMacros.h>
# include <Files.h>
#endif

      ],
      [

        short                vRefNum;
        long                 dirID;
        ConstStr255Param     fileName;

        Boolean*             isDirectory;
        UInt8*               path;
        SInt16               desiredRefNum;
        SInt16*              iterator;
        SInt16*              actualRefNum;
        HFSUniStr255*        outForkName;
        FSVolumeRefNum       volume;
        FSCatalogInfoBitmap  whichInfo;
        FSCatalogInfo*       catalogInfo;
        FSForkInfo*          forkInfo;
        FSRef*               ref;

#if HAVE_FSSPEC
        FSSpec*              spec;
#endif

        /* FSRef functions: no need to check? */
        FSGetForkCBInfo( desiredRefNum, volume, iterator,
                         actualRefNum, forkInfo, ref,
                         outForkName );
        FSPathMakeRef( path, ref, isDirectory );

#if HAVE_FSSPEC
        FSpMakeFSRef ( spec, ref );
        FSGetCatalogInfo( ref, whichInfo, catalogInfo,
                          outForkName, spec, ref );
#endif
      ])],
    [AC_MSG_RESULT([ok])
     CFLAGS="$CFLAGS -DHAVE_FSREF=1"],
    [AC_MSG_RESULT([not found])
     CFLAGS="$CFLAGS -DHAVE_FSREF=0"])
fi


# Whether to use QuickDraw API in ToolBox, which is deprecated since
# Mac OS X 10.4.

AC_ARG_WITH([quickdraw-toolbox],
  AS_HELP_STRING([--with-quickdraw-toolbox],
                 [use MacOS QuickDraw in ToolBox, if available (default=yes)]))
if test x$with_quickdraw_toolbox = xno; then
  CFLAGS="$CFLAGS -DHAVE_QUICKDRAW_TOOLBOX=0"
elif test x$with_old_mac_fonts = xyes -a x$with_quickdraw_toolbox != x; then
  AC_MSG_CHECKING([QuickDraw FontManager functions in ToolBox])
  AC_LINK_IFELSE([
    AC_LANG_PROGRAM([

#if defined(__GNUC__) && defined(__APPLE_CC__)
# include <CoreServices/CoreServices.h>
# include <ApplicationServices/ApplicationServices.h>
#else
# include <ConditionalMacros.h>
# include <Fonts.h>
#endif

      ],
      [

        Str255     familyName;
        SInt16     familyID   = 0;
        FMInput*   fmIn       = NULL;
        FMOutput*  fmOut      = NULL;


        GetFontName( familyID, familyName );
        GetFNum( familyName, &familyID );
        fmOut = FMSwapFont( fmIn );

      ])],
    [AC_MSG_RESULT([ok])
     CFLAGS="$CFLAGS -DHAVE_QUICKDRAW_TOOLBOX=1"],
    [AC_MSG_RESULT([not found])
     CFLAGS="$CFLAGS -DHAVE_QUICKDRAW_TOOLBOX=0"])
fi


# Whether to use QuickDraw API in Carbon, which is deprecated since
# Mac OS X 10.4.

AC_ARG_WITH([quickdraw-carbon],
  AS_HELP_STRING([--with-quickdraw-carbon],
                 [use MacOS QuickDraw in Carbon, if available (default=yes)]))
if test x$with_quickdraw_carbon = xno; then
  CFLAGS="$CFLAGS -DHAVE_QUICKDRAW_CARBON=0"
elif test x$with_old_mac_fonts = xyes -a x$with_quickdraw_carbon != x; then
  AC_MSG_CHECKING([QuickDraw FontManager functions in Carbon])
  AC_LINK_IFELSE([
    AC_LANG_PROGRAM([

#if defined(__GNUC__) && defined(__APPLE_CC__)
# include <CoreServices/CoreServices.h>
# include <ApplicationServices/ApplicationServices.h>
#else
# include <ConditionalMacros.h>
# include <Fonts.h>
#endif

      ],
      [

        FMFontFamilyIterator          famIter;
        FMFontFamily                  family;
        Str255                        famNameStr;
        FMFontFamilyInstanceIterator  instIter;
        FMFontStyle                   style;
        FMFontSize                    size;
        FMFont                        font;
        FSSpec*                       pathSpec;


        FMCreateFontFamilyIterator( NULL, NULL, kFMUseGlobalScopeOption,
                                    &famIter );
        FMGetNextFontFamily( &famIter, &family );
        FMGetFontFamilyName( family, famNameStr );
        FMCreateFontFamilyInstanceIterator( family, &instIter );
        FMGetNextFontFamilyInstance( &instIter, &font, &style, &size );
        FMDisposeFontFamilyInstanceIterator( &instIter );
        FMDisposeFontFamilyIterator( &famIter );
        FMGetFontContainer( font, pathSpec );

      ])],
    [AC_MSG_RESULT([ok])
     CFLAGS="$CFLAGS -DHAVE_QUICKDRAW_CARBON=1"],
    [AC_MSG_RESULT([not found])
     CFLAGS="$CFLAGS -DHAVE_QUICKDRAW_CARBON=0"])
fi


# Whether to use AppleTypeService since Mac OS X.

AC_ARG_WITH([ats],
  AS_HELP_STRING([--with-ats],
                 [use AppleTypeService, if available (default=yes)]))
if test x$with_ats = xno; then
  CFLAGS="$CFLAGS -DHAVE_ATS=0"
elif test x$with_old_mac_fonts = xyes -a x$with_ats != x; then
  AC_MSG_CHECKING([AppleTypeService functions])
  AC_LINK_IFELSE([
    AC_LANG_PROGRAM([

#if defined(__GNUC__) && defined(__APPLE_CC__)
# include <CoreServices/CoreServices.h>
# include <ApplicationServices/ApplicationServices.h>
#else
# include <ConditionalMacros.h>
# include <Files.h>
#endif

      ],
      [

        FSSpec*  pathSpec;


        ATSFontFindFromName( NULL, kATSOptionFlagsUnRestrictedScope );
#if HAVE_FSSPEC
        ATSFontGetFileSpecification( 0, pathSpec );
#endif

      ])],
    [AC_MSG_RESULT([ok])
     CFLAGS="$CFLAGS -DHAVE_ATS=1"],
    [AC_MSG_RESULT([not found])
     CFLAGS="$CFLAGS -DHAVE_ATS=0"])
fi

case "$CFLAGS" in
  *HAVE_FSSPEC* | *HAVE_FSREF* | *HAVE_QUICKDRAW* | *HAVE_ATS* )
    AC_MSG_WARN([
*** WARNING
    FSSpec/FSRef/QuickDraw/ATS options are explicitly given,
    thus it is recommended to replace src/base/ftmac.c by builds/mac/ftmac.c.
    ])
    CFLAGS="$CFLAGS "'-I$(TOP_DIR)/builds/mac/'
    ;;
  *)
    ;;
esac

# Check for pthreads

AX_PTHREAD([have_pthread=yes], [have_pthread=no])

# Check for Python and docwriter
PYTHON_MIN_VERSION=3.5
have_py3=no
have_docwriter=no
PIP=pip

AC_CHECK_PROGS([PYTHON], [python3 python], [missing])
if test "x$PYTHON" != "xmissing"; then
  AX_PROG_PYTHON_VERSION([$PYTHON_MIN_VERSION], [have_py3=yes], [])

  if test "x$have_py3" = "xyes"; then
    PIP="$PYTHON -m $PIP"
    AC_MSG_CHECKING([for `docwriter' Python module])
    $PYTHON -m docwriter -h > /dev/null 2>&1
    if test "x$?" = "x0"; then
      have_docwriter=yes
      AC_MSG_RESULT([yes])
    else
      AC_MSG_RESULT([no])
    fi
  fi
fi


# entries in Requires.private are separated by commas
PKGCONFIG_REQUIRES_PRIVATE="$zlib_reqpriv,     \
                            $bzip2_reqpriv,    \
                            $libpng_reqpriv,   \
                            $harfbuzz_reqpriv, \
                            $brotli_reqpriv"
# beautify
PKGCONFIG_REQUIRES_PRIVATE=`echo "$PKGCONFIG_REQUIRES_PRIVATE" \
                            | sed -e 's/^  *//'      \
                                  -e 's/  *$//'      \
                                  -e 's/, */,/g'     \
                                  -e 's/,,*/,/g'     \
                                  -e 's/^,*//'       \
                                  -e 's/,*$//'       \
                                  -e 's/,/, /g'`

PKGCONFIG_LIBS_PRIVATE="$zlib_libspriv     \
                        $bzip2_libspriv    \
                        $libpng_libspriv   \
                        $harfbuzz_libspriv \
                        $brotli_libspriv   \
                        $ft2_extra_libs"
# beautify
PKGCONFIG_LIBS_PRIVATE=`echo "$PKGCONFIG_LIBS_PRIVATE"  \
                        | sed -e 's/^  *//'   \
                              -e 's/  *$//'   \
                              -e 's/  */ /g'`

LIBSSTATIC_CONFIG="-lfreetype               \
                   $zlib_libsstaticconf     \
                   $bzip2_libsstaticconf    \
                   $libpng_libsstaticconf   \
                   $harfbuzz_libsstaticconf \
                   $brotli_libsstaticconf   \
                   $ft2_extra_libs"
# remove -L/usr/lib and -L/usr/lib64 since `freetype-config' adds them later
# on if necessary; also beautify
LIBSSTATIC_CONFIG=`echo "$LIBSSTATIC_CONFIG"          \
                   | sed -e 's|-L */usr/lib64/* | |g' \
                         -e 's|-L */usr/lib/* | |g'   \
                         -e 's/^  *//'                \
                         -e 's/  *$//'                \
                         -e 's/  */ /g'`

# If FreeType gets installed with `--disable-shared', don't use
# 'private' fields.  `pkg-config' only looks into `.pc' files and is
# completely agnostic to whether shared libraries are actually present
# or not.  As a consequence, the user had to specify `--static' while
# calling `pkg-config', which configure scripts are normally not
# prepared for.

PKGCONFIG_REQUIRES=
PKGCONFIG_LIBS='-L${libdir} -lfreetype'

if test $enable_shared = "no"; then
  PKGCONFIG_REQUIRES="$PKGCONFIG_REQUIRES $PKGCONFIG_REQUIRES_PRIVATE"
  PKGCONFIG_REQUIRES_PRIVATE=
  PKGCONFIG_LIBS="$PKGCONFIG_LIBS $PKGCONFIG_LIBS_PRIVATE"
  PKGCONFIG_LIBS_PRIVATE=
fi

AC_SUBST([ftmac_c])
AC_SUBST([PKGCONFIG_REQUIRES])
AC_SUBST([PKGCONFIG_LIBS])
AC_SUBST([PKGCONFIG_REQUIRES_PRIVATE])
AC_SUBST([PKGCONFIG_LIBS_PRIVATE])
AC_SUBST([LIBSSTATIC_CONFIG])

AC_SUBST([hardcode_libdir_flag_spec])
AC_SUBST([wl])
AC_SUBST([build_libtool_libs])


# changing LDFLAGS value should only be done after
# lt_cv_prog_compiler_static_works test

ftoption_set()
{
  regexp="-e \\\"s|.*#.*def.*$1\>.*|#define $1|\\\""
  FTOPTION_H_SED="$FTOPTION_H_SED $regexp"
}

ftoption_unset()
{
  regexp="-e \\\"s|.*#.*def.*$1\>.*|/* #undef $1 */|\\\""
  FTOPTION_H_SED="$FTOPTION_H_SED $regexp"
}

if test "$have_zlib" != no; then
  CFLAGS="$CFLAGS $ZLIB_CFLAGS"
  LDFLAGS="$LDFLAGS $ZLIB_LIBS"
  ftoption_set FT_CONFIG_OPTION_SYSTEM_ZLIB
else
  ftoption_unset FT_CONFIG_OPTION_SYSTEM_ZLIB
fi
if test "$have_bzip2" != no; then
  CFLAGS="$CFLAGS $BZIP2_CFLAGS"
  LDFLAGS="$LDFLAGS $BZIP2_LIBS"
  ftoption_set FT_CONFIG_OPTION_USE_BZIP2
else
  ftoption_unset FT_CONFIG_OPTION_USE_BZIP2
fi
if test "$have_libpng" != no; then
  CFLAGS="$CFLAGS $LIBPNG_CFLAGS"
  LDFLAGS="$LDFLAGS $LIBPNG_LIBS"
  ftoption_set FT_CONFIG_OPTION_USE_PNG
else
  ftoption_unset FT_CONFIG_OPTION_USE_PNG
fi
if test "$have_harfbuzz" != no; then
  CFLAGS="$CFLAGS $HARFBUZZ_CFLAGS"
  LDFLAGS="$LDFLAGS $HARFBUZZ_LIBS"
  ftoption_set FT_CONFIG_OPTION_USE_HARFBUZZ
else
  ftoption_unset FT_CONFIG_OPTION_USE_HARFBUZZ
fi
if test "$have_harfbuzz_dynamic" != no; then
  ftoption_set FT_CONFIG_OPTION_USE_HARFBUZZ_DYNAMIC
else
  ftoption_unset FT_CONFIG_OPTION_USE_HARFBUZZ_DYNAMIC
fi
if test "$have_brotli" != no; then
  CFLAGS="$CFLAGS $BROTLI_CFLAGS"
  LDFLAGS="$LDFLAGS $BROTLI_LIBS"
  ftoption_set FT_CONFIG_OPTION_USE_BROTLI
else
  ftoption_unset FT_CONFIG_OPTION_USE_BROTLI
fi

if test "$have_pthread" != no; then
  CFLAGS="$CFLAGS $PTHREAD_CFLAGS"
  LDFLAGS="$LDFLAGS $PTHREAD_CFLAGS $PTHREAD_LIBS"
fi

AC_SUBST([CFLAGS])
AC_SUBST([LDFLAGS])

# We don't want to use a template file for `ftoption.h', since compilation
# should work without calling a configure script also.  For this reason, we
# copy the `include/freetype/config/ftoption.h' file to the `unix/builds'
# directory (using a dummy `AC_CONFIG_FILES' call) and apply the just
# constructed $FTOPTION_H_SED regexp (using the post-action of
# `AC_CONFIG_FILES'); this is also the version that gets installed later on.
#
AC_CONFIG_FILES([ftoption.h:${srcdir}/../../include/freetype/config/ftoption.h],
  [mv ftoption.h ftoption.tmp
   eval "sed $FTOPTION_H_SED < ftoption.tmp > ftoption.h"
   rm ftoption.tmp],
  [FTOPTION_H_SED="$FTOPTION_H_SED"])

AC_CONFIG_HEADERS([ftconfig.h])

# create the Unix-specific sub-Makefiles `builds/unix/unix-def.mk'
# and `builds/unix/unix-cc.mk' that will be used by the build system
#
AC_CONFIG_FILES([unix-cc.mk:unix-cc.in
                 unix-def.mk:unix-def.in])

AC_OUTPUT

AC_MSG_NOTICE([

Library configuration:
  external zlib: $have_zlib
  bzip2:         $have_bzip2
  libpng:        $have_libpng
  harfbuzz:      $have_harfbuzz
  brotli:        $have_brotli
  pthread:       $have_pthread
])

# Warn if docwriter is not installed

if test $have_docwriter = no; then
  AC_MSG_WARN([
  `make refdoc' will fail since pip package `docwriter' is not installed.
  To install, run `$PIP install docwriter', or to use a Python
  virtual environment, run `make refdoc-venv' (requires pip package
  `virtualenv').  These operations require Python >= $PYTHON_MIN_VERSION.
  ])
fi

# Warn if pthread is not available

if test $have_pthread = no; then
  AC_MSG_WARN([
  `FT_DEBUG_LOGGING' will not work since the `pthread' library is not
  available.  This warning can be safely ignored if you don't plan to use
  this configuration macro.
  ])
fi

# end of configure.raw
