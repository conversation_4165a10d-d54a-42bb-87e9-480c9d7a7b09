<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{05d76552-ed33-44e3-81b3-8b0006ca45e9}</ProjectGuid>
    <RootNamespace>dmadayzcpp</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <TargetName>DayZ-DMA</TargetName>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>$(SolutionDir)DMALib\includes;$(SolutionDir)DMALib\imgui;$(SolutionDir)DMALib\imgui\backends;$(SolutionDir)DMALib\imgui\misc\cpp</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(SolutionDir)DMALib\libs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>$(SolutionDir)DMALib\includes;$(SolutionDir)DMALib\imgui;$(SolutionDir)DMALib\imgui\backends;$(SolutionDir)DMALib\imgui\misc\cpp</AdditionalIncludeDirectories>
      <Optimization>MaxSpeed</Optimization>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(SolutionDir)DMALib\libs;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>%(AdditionalDependencies)</AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="DayZ\DayZMem\DayZMem.cpp" />
    <ClCompile Include="DayZ\DayZMem\MemoryUpdater\MemoryUpdater.cpp" />
    <ClCompile Include="DayZ\DayZMem\OverlayAdapter\OverlayAdapter.cpp" />
    <ClCompile Include="DayZ\DayZMem\RadarAdapter\RadarAdapter.cpp" />
    <ClCompile Include="DayZ\DayzUtil.cpp" />
    <ClCompile Include="DayZ\Maps\Alteria.cpp" />
    <ClCompile Include="DayZ\Maps\Banov.cpp" />
    <ClCompile Include="DayZ\Maps\ChernarusPlus.cpp" />
    <ClCompile Include="DayZ\Maps\DeadFall.cpp" />
    <ClCompile Include="DayZ\Maps\Deerisle.cpp" />
    <ClCompile Include="DayZ\Maps\Esseker.cpp" />
    <ClCompile Include="DayZ\Maps\Livonia.cpp" />
    <ClCompile Include="DayZ\Maps\Lux.cpp" />
    <ClCompile Include="DayZ\Maps\Melkart.cpp" />
    <ClCompile Include="DayZ\Maps\Namalsk.cpp" />
    <ClCompile Include="DayZ\Maps\Sakhal.cpp" />
    <ClCompile Include="DayZ\Maps\Takistan.cpp" />
    <ClCompile Include="DayZ\Structs\Entity.cpp" />
    <ClCompile Include="dma-dayz-cpp.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="DayZ\DayZMem\DayZMem.h" />
    <ClInclude Include="DayZ\DayZMem\MemoryUpdater\MemoryUpdater.h" />
    <ClInclude Include="DayZ\DayZMem\OverlayAdapter\OverlayAdapter.h" />
    <ClInclude Include="DayZ\DayZMem\RadarAdapter\RadarAdapter.h" />
    <ClInclude Include="DayZ\DayzUtil.h" />
    <ClInclude Include="DayZ\Maps\Alteria.h" />
    <ClInclude Include="DayZ\Maps\Banov.h" />
    <ClInclude Include="DayZ\Maps\ChernarusPlus.h" />
    <ClInclude Include="DayZ\Maps\DeadFall.h" />
    <ClInclude Include="DayZ\Maps\Deerisle.h" />
    <ClInclude Include="DayZ\Maps\Esseker.h" />
    <ClInclude Include="DayZ\Maps\Livonia.h" />
    <ClInclude Include="DayZ\Maps\Lux.h" />
    <ClInclude Include="DayZ\Maps\Melkart.h" />
    <ClInclude Include="DayZ\Maps\Namalsk.h" />
    <ClInclude Include="DayZ\Maps\Sakhal.h" />
    <ClInclude Include="DayZ\Maps\Takistan.h" />
    <ClInclude Include="DayZ\Structs\ArmaString.h" />
    <ClInclude Include="DayZ\Structs\EntityFilterList.h" />
    <ClInclude Include="DayZ\Structs\Camera.h" />
    <ClInclude Include="DayZ\Structs\Entity.h" />
    <ClInclude Include="DayZ\Structs\EntityInventory.h" />
    <ClInclude Include="DayZ\Structs\EntityTable.h" />
    <ClInclude Include="DayZ\Structs\EntityTableSlowItem.h" />
    <ClInclude Include="DayZ\Structs\EntityType.h" />
    <ClInclude Include="DayZ\Structs\FutureVisualState.h" />
    <ClInclude Include="DayZ\Structs\InventoryItem.h" />
    <ClInclude Include="DayZ\Structs\WorldNoLists.h" />
    <ClInclude Include="DayZ\Structs\WorldPointer.h" />
    <ClInclude Include="DayZ\Structs\World.h" />
    <ClInclude Include="DayZ\Structs\NetworkClient.h" />
    <ClInclude Include="DayZ\Structs\NetworkManager.h" />
    <ClInclude Include="DayZ\Structs\Scoreboard.h" />
    <ClInclude Include="DayZ\Structs\ScoreboardIdentity.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="Skeleton.h" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DMALib\DMALib.vcxproj">
      <Project>{387d38d2-6ad1-4118-ab1d-9e817040136e}</Project>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <CopyFileToFolders Include="mapcontent\chernarusplus.png">
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(OutDir)\maps</DestinationFolders>
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(OutDir)\maps</DestinationFolders>
    </CopyFileToFolders>
  </ItemGroup>
  <ItemGroup>
    <CopyFileToFolders Include="mapcontent\namalsk.png">
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(OutDir)\maps</DestinationFolders>
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(OutDir)\maps</DestinationFolders>
    </CopyFileToFolders>
  </ItemGroup>
  <ItemGroup>
    <CopyFileToFolders Include="mapcontent\livonia.png">
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(OutDir)\maps</DestinationFolders>
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(OutDir)\maps</DestinationFolders>
    </CopyFileToFolders>
  </ItemGroup>
  <ItemGroup>
    <CopyFileToFolders Include="mapcontent\alteria.png">
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(OutDir)\maps</DestinationFolders>
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(OutDir)\maps</DestinationFolders>
    </CopyFileToFolders>
    <CopyFileToFolders Include="mapcontent\banov.png">
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(OutDir)\maps</DestinationFolders>
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(OutDir)\maps</DestinationFolders>
    </CopyFileToFolders>
    <CopyFileToFolders Include="mapcontent\deadfall.png">
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(OutDir)\maps</DestinationFolders>
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(OutDir)\maps</DestinationFolders>
    </CopyFileToFolders>
    <CopyFileToFolders Include="mapcontent\deerisle.png">
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(OutDir)\maps</DestinationFolders>
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(OutDir)\maps</DestinationFolders>
    </CopyFileToFolders>
    <CopyFileToFolders Include="mapcontent\melkart.png">
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(OutDir)\maps</DestinationFolders>
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(OutDir)\maps</DestinationFolders>
    </CopyFileToFolders>
    <CopyFileToFolders Include="mapcontent\sakhal.png">
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(OutDir)\maps</DestinationFolders>
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(OutDir)\maps</DestinationFolders>
    </CopyFileToFolders>
    <CopyFileToFolders Include="mapcontent\takistan.png">
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(OutDir)\maps</DestinationFolders>
      <DestinationFolders Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(OutDir)\maps</DestinationFolders>
    </CopyFileToFolders>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="dma-dayz-cpp.rc" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="53ac2d01e41ff949c11c916309ae17b7.ico" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>