
imgui_stdlib.h + imgui_stdlib.cpp
  InputText() wrappers for C++ standard library (STL) type: std::string.
  This is also an example of how you may wrap your own similar types.

imgui_scoped.h
  [Experimental, not currently in main repository]
  Additional header file with some RAII-style wrappers for common Dear ImGui functions.
  Try by merging: https://github.com/ocornut/imgui/pull/2197
  Discuss at: https://github.com/ocornut/imgui/issues/2096

See more C++ related extension (fmt, RAII, syntaxis sugar) on Wiki:
  https://github.com/ocornut/imgui/wiki/Useful-Extensions#cness
