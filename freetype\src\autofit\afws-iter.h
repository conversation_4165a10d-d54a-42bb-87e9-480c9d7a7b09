/****************************************************************************
 *
 * afws-iter.h
 *
 *   Auto-fitter writing systems iterator (specification only).
 *
 * Copyright (C) 2013-2024 by
 * <PERSON>, <PERSON>, and <PERSON>.
 *
 * This file is part of the FreeType project, and may only be used,
 * modified, and distributed under the terms of the FreeType project
 * license, LICENSE.TXT.  By continuing to use, modify, or distribute
 * this file you indicate that you have read the license and
 * understand and accept it fully.
 *
 */

  /* This header may be included multiple times. */
  /* Define `WRITING_SYSTEM' as needed.          */


  /* Add new writing systems here.  The arguments are the writing system */
  /* name in lowercase and uppercase, respectively.                      */

  WRITING_SYSTEM( dummy, DUMMY )
  WRITING_SYSTEM( latin, LATIN )
  WRITING_SYSTEM( cjk,   CJK   )
  WRITING_SYSTEM( indic, INDIC )


/* END */
