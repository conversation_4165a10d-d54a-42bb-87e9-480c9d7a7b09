## OSX artifacts
.DS_Store

## Dear ImGui artifacts
imgui.ini

## General build artifacts
*.o
*.obj
*.exe
examples/*/Debug/*
examples/*/Release/*
examples/*/x64/*

## Visual Studio artifacts
.vs
ipch
*.opensdf
*.log
*.pdb
*.ilk
*.user
*.sdf
*.suo
*.VC.db
*.VC.VC.opendb

## Getting files created in JSON/Schemas/Catalog/ from a VS2022 update
JSON/

## Commonly used CMake directories
build*/

## Xcode artifacts
project.xcworkspace
xcuserdata

## Emscripten artifacts
examples/*.o.tmp
examples/*.out.js
examples/*.out.wasm
examples/example_glfw_opengl3/web/*
examples/example_sdl2_opengl3/web/*
examples/example_emscripten_wgpu/web/*

## JetBrains IDE artifacts
.idea
cmake-build-*

## Unix executables from our example Makefiles
examples/example_glfw_metal/example_glfw_metal
examples/example_glfw_opengl2/example_glfw_opengl2
examples/example_glfw_opengl3/example_glfw_opengl3
examples/example_glut_opengl2/example_glut_opengl2
examples/example_null/example_null
examples/example_sdl2_metal/example_sdl2_metal
examples/example_sdl2_opengl2/example_sdl2_opengl2
examples/example_sdl2_opengl3/example_sdl2_opengl3
examples/example_sdl2_sdlrenderer/example_sdl2_sdlrenderer
